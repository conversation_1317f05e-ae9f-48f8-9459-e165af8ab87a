import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { AMNAService } from '../../amna/amnaService';
import { CentralChatService } from '../core/centralChatService';
import { IntegrationOrchestrator } from '../core/integrationOrchestrator';
import { LighthouseIntegration } from '../connectors/lighthouseIntegration';
import { TrainingVendorIntegration } from '../connectors/trainingVendorIntegration';
import { WinsIntegration } from '../connectors/winsIntegration';
import { WebSocketManager } from '../core/websocketManager';
import { CacheService } from '../core/cacheService';
import { OfflineQueueService } from '../core/offlineQueueService';

// Mock fetch for API calls
global.fetch = vi.fn();

describe('Cross-Application Integration Tests', () => {
  let amnaService: AMNAService;
  let centralChatService: CentralChatService;
  let orchestrator: IntegrationOrchestrator;
  let lighthouseIntegration: LighthouseIntegration;
  let trainingVendorIntegration: TrainingVendorIntegration;
  let winsIntegration: WinsIntegration;
  let wsManager: WebSocketManager;
  let cacheService: CacheService;
  let offlineQueue: OfflineQueueService;

  beforeEach(() => {
    // Reset singletons
    (AMNAService as any).instance = null;
    (CentralChatService as any).instance = null;
    (IntegrationOrchestrator as any).instance = null;
    (WebSocketManager as any).instance = null;
    (CacheService as any).instance = null;
    (OfflineQueueService as any).instance = null;

    // Initialize services
    cacheService = CacheService.getInstance();
    offlineQueue = OfflineQueueService.getInstance();
    wsManager = WebSocketManager.getInstance({
      url: 'ws://localhost:3001'
    });
    
    // Mock WebSocket connection
    vi.spyOn(wsManager, 'isConnected').mockReturnValue(true);
    vi.spyOn(wsManager, 'send').mockResolvedValue(undefined);

    // Initialize integrations
    lighthouseIntegration = new LighthouseIntegration();
    trainingVendorIntegration = new TrainingVendorIntegration();
    winsIntegration = new WinsIntegration();

    // Initialize core services
    amnaService = AMNAService.getInstance();
    orchestrator = IntegrationOrchestrator.getInstance(cacheService, wsManager);
    centralChatService = CentralChatService.getInstance(
      orchestrator,
      wsManager,
      cacheService,
      offlineQueue,
      amnaService
    );

    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Research-to-Training Flow', () => {
    it('should identify training needs from research insights', async () => {
      // Mock Lighthouse research data indicating skill gaps
      const researchData = {
        researches: [{
          id: 'res-1',
          title: 'Python Skills Gap Analysis',
          summary: 'Team lacks advanced Python skills',
          insights: ['Need for Python training', 'OOP concepts missing'],
          category: 'skills',
          tags: ['python', 'training', 'skill-gap']
        }],
        insights: [{
          id: 'ins-1',
          topic: 'Training Requirements',
          content: 'Based on project needs, Python training is critical',
          relevance: 0.9,
          applications: ['training'],
          recommendations: ['Immediate Python training needed']
        }]
      };

      // Mock training vendor data
      const vendorData = {
        vendors: [{
          id: 'vendor-1',
          name: 'Python Academy',
          capabilities: [{
            skill: 'Python',
            proficiencyLevels: ['beginner', 'intermediate', 'advanced'],
            priceRange: { min: 1000, max: 2000 }
          }],
          rating: 4.8
        }]
      };

      // Mock API responses
      (global.fetch as any)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ status: 'healthy' })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ status: 'healthy' })
        });

      // Set up data in orchestrator
      orchestrator.syncIntegration = vi.fn()
        .mockResolvedValueOnce(researchData)
        .mockResolvedValueOnce(vendorData);

      // Process a message asking about training based on research
      const message = {
        id: 'msg-1',
        content: 'What training do we need based on our research insights?',
        role: 'user' as const,
        timestamp: new Date(),
        metadata: { userId: 'user123' }
      };

      const response = await centralChatService.sendMessage(
        message.content,
        { includeContext: true, applications: ['lighthouse', 'training'] }
      );

      // Verify cross-application context was gathered
      expect(centralChatService.sendMessage).toBeDefined();
      
      // Verify activity was logged
      const activityHandler = vi.fn();
      winsIntegration.on('activity.processed', activityHandler);

      // Simulate the flow
      await lighthouseIntegration.processActivity({
        type: 'research_completed',
        data: researchData.researches[0],
        userId: 'user123',
        timestamp: new Date()
      });

      // The system should identify training needs from research
      const synthesis = await orchestrator.synthesizeResponse(
        'training needs based on research',
        { applications: ['lighthouse', 'training'] }
      );

      expect(synthesis.recommendations).toBeDefined();
    });
  });

  describe('Email-to-Wins Flow', () => {
    it('should track email automation creation as a win', async () => {
      const winsActivityHandler = vi.fn();
      winsIntegration.on('activity.recorded', winsActivityHandler);

      // Simulate email automation creation
      const emailActivity = {
        userId: 'user123',
        source: 'eConnect' as const,
        type: 'automation_created',
        data: {
          automationId: 'auto-1',
          type: 'follow-up',
          estimatedTimeSaving: 120 // minutes
        }
      };

      await winsIntegration.processActivity(emailActivity);

      // Should record as an achievement
      expect(winsActivityHandler).toHaveBeenCalledWith(
        expect.objectContaining({
          source: 'eConnect',
          type: 'automation_created',
          points: expect.any(Number)
        })
      );
    });
  });

  describe('Vendor-Training-Wins Integration', () => {
    it('should track successful training completion across all systems', async () => {
      const trainingCompletion = {
        trainingNeedId: 'need-1',
        vendorId: 'vendor-1',
        participants: ['user1', 'user2', 'user3'],
        completionDate: new Date(),
        feedback: {
          overallRating: 4.8,
          wouldRecommend: true,
          comments: 'Excellent training',
          skillsAcquired: ['Python basics', 'OOP', 'Testing']
        }
      };

      // Mock successful API call
      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true })
      });

      // Process completion in training system
      const success = await trainingVendorIntegration.pushTrainingCompletion(
        trainingCompletion
      );

      expect(success).toBe(true);

      // Should also trigger wins tracking for each participant
      const winsHandler = vi.fn();
      winsIntegration.on('activity.processed', winsHandler);

      // Simulate the activity processing
      for (const participant of trainingCompletion.participants) {
        await winsIntegration.processActivity({
          userId: participant,
          source: 'training',
          type: 'training_completed',
          data: {
            trainingNeedId: trainingCompletion.trainingNeedId,
            vendorId: trainingCompletion.vendorId,
            skillsAcquired: trainingCompletion.feedback.skillsAcquired,
            rating: trainingCompletion.feedback.overallRating
          }
        });
      }
    });
  });

  describe('AMNA Context Synthesis', () => {
    it('should synthesize context from multiple applications', async () => {
      // Set up mock data for each integration
      const mockContexts = {
        eConnect: {
          recentEmails: [{ subject: 'Python Training Request' }],
          automations: []
        },
        lighthouse: {
          researches: [{ title: 'Python Skills Assessment' }],
          insights: [{ content: 'Python training is priority' }]
        },
        training: {
          trainingNeeds: [{ skillName: 'Python', priority: 'high' }],
          vendors: [{ name: 'Python Academy', rating: 4.8 }]
        }
      };

      // Mock context gathering
      vi.spyOn(centralChatService as any, 'gatherEnhancedContext')
        .mockResolvedValue({
          base: {},
          integrations: mockContexts,
          crossApp: {
            connections: [{
              type: 'email-research-training',
              data: 'Email request aligns with research findings and training needs'
            }]
          },
          insights: [
            'Found training request in emails',
            'Research confirms training need',
            'Vendor available for training'
          ]
        });

      // Send a cross-application query
      const response = await centralChatService.sendMessage(
        'Show me how our email requests align with research and training needs',
        {
          includeContext: true,
          applications: ['eConnect', 'lighthouse', 'training']
        }
      );

      // Verify enhanced context was used
      expect(centralChatService.sendMessage).toBeDefined();
    });
  });

  describe('Real-time Synchronization', () => {
    it('should propagate updates across applications in real-time', async () => {
      const updateHandlers = {
        lighthouse: vi.fn(),
        training: vi.fn(),
        wins: vi.fn()
      };

      // Subscribe to updates
      orchestrator.on('data.updated', updateHandlers.lighthouse);
      orchestrator.on('data.updated', updateHandlers.training);
      winsIntegration.on('activity.processed', updateHandlers.wins);

      // Simulate a research completion that triggers updates
      const researchUpdate = {
        source: 'lighthouse',
        data: {
          research: {
            id: 'res-new',
            title: 'New Skills Framework',
            insights: ['Need for comprehensive retraining']
          }
        }
      };

      // Emit update through orchestrator
      orchestrator.emit('data.updated', researchUpdate);

      // All handlers should receive the update
      expect(updateHandlers.lighthouse).toHaveBeenCalledWith(researchUpdate);
      expect(updateHandlers.training).toHaveBeenCalledWith(researchUpdate);

      // WebSocket should broadcast the update
      expect(wsManager.send).toBeDefined();
    });
  });

  describe('Intelligent Recommendations', () => {
    it('should generate cross-application recommendations', async () => {
      // Mock current state with data from multiple apps
      vi.spyOn(orchestrator as any, 'latestData', 'get').mockReturnValue(
        new Map([
          ['training', {
            trainingNeeds: [
              { skillName: 'Python', priority: 'critical', employeeCount: 20 }
            ]
          }],
          ['vendors', {
            vendors: [
              { name: 'Python Pro', rating: 4.9, contractStatus: 'active' }
            ]
          }],
          ['lighthouse', {
            insights: [
              { topic: 'Python', relevance: 0.95, content: 'Critical for Q4 projects' }
            ]
          }]
        ])
      );

      const recommendations = await orchestrator.generateRecommendations({
        applications: ['training', 'vendors', 'lighthouse']
      });

      // Should generate cross-app recommendations
      expect(recommendations).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            applications: expect.arrayContaining(['training', 'vendors'])
          })
        ])
      );
    });
  });

  describe('Activity Aggregation', () => {
    it('should aggregate activities from all applications for weekly report', async () => {
      const activities = [
        {
          userId: 'user123',
          source: 'eConnect' as const,
          type: 'email_sent',
          timestamp: new Date()
        },
        {
          userId: 'user123',
          source: 'lighthouse' as const,
          type: 'research_completed',
          timestamp: new Date()
        },
        {
          userId: 'user123',
          source: 'training' as const,
          type: 'assessment_completed',
          timestamp: new Date()
        }
      ];

      // Process activities
      for (const activity of activities) {
        await winsIntegration.processActivity({
          ...activity,
          data: {}
        });
      }

      // Generate weekly report
      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          userId: 'user123',
          weekNumber: 45,
          year: 2024,
          totalActivities: 3,
          totalPoints: 95,
          wins: [],
          achievements: [],
          topCategories: [
            { category: 'productivity', count: 1, points: 15 },
            { category: 'learning', count: 2, points: 80 }
          ],
          streak: 5,
          insights: ['Consistent activity across all applications']
        })
      });

      const report = await winsIntegration.generateWeeklyReport('user123');

      expect(report).toMatchObject({
        totalActivities: 3,
        insights: expect.arrayContaining(['Consistent activity across all applications'])
      });
    });
  });

  describe('Error Recovery', () => {
    it('should handle integration failures gracefully', async () => {
      // Mock a failing integration
      (global.fetch as any).mockRejectedValueOnce(new Error('Network error'));

      const errorHandler = vi.fn();
      orchestrator.on('sync.failed', errorHandler);

      // Attempt to sync
      try {
        await orchestrator.syncIntegration('lighthouse');
      } catch (error) {
        // Expected to fail
      }

      expect(errorHandler).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'lighthouse',
          error: expect.any(Error)
        })
      );

      // Other integrations should continue working
      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: 'success' })
      });

      const trainingSync = await orchestrator.syncIntegration('training');
      expect(trainingSync).toBeDefined();
    });
  });

  describe('Performance', () => {
    it('should handle concurrent operations efficiently', async () => {
      const startTime = Date.now();

      // Mock all API responses
      (global.fetch as any).mockImplementation(() => 
        Promise.resolve({
          ok: true,
          json: async () => ({ data: 'test' })
        })
      );

      // Run multiple operations concurrently
      const operations = [
        orchestrator.syncIntegration('lighthouse'),
        orchestrator.syncIntegration('training'),
        orchestrator.syncIntegration('vendors'),
        orchestrator.generateRecommendations({}),
        centralChatService.sendMessage('Test', { includeContext: true })
      ];

      await Promise.all(operations);

      const duration = Date.now() - startTime;
      
      // All operations should complete within reasonable time
      expect(duration).toBeLessThan(2000); // 2 seconds for all operations
    });
  });
});