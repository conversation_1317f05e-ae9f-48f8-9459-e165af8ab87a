import { useEffect, useState, useCallback, create<PERSON>ontext, useContext, ReactNode } from 'react'
import { useNavigate } from '@tanstack/react-router'
import { AuthService, LoginCredentials, LoginResponse } from '~/services/api/auth.service'
import { apiClient } from '~/api/client'

interface AuthContextType {
  user: LoginResponse['user'] | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (credentials: LoginCredentials) => Promise<void>
  logout: () => Promise<void>
  refreshAuth: () => Promise<void>
  error: string | null
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<LoginResponse['user'] | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const navigate = useNavigate()

  // Initialize auth state from stored token
  useEffect(() => {
    const initAuth = async () => {
      const token = apiClient.getAuthToken()
      if (token) {
        try {
          const currentUser = await AuthService.getCurrentUser()
          setUser(currentUser)
        } catch (err) {
          // Token might be expired, try to refresh
          try {
            await AuthService.refreshToken()
            const currentUser = await AuthService.getCurrentUser()
            setUser(currentUser)
          } catch (refreshErr) {
            // Refresh failed, clear auth state
            apiClient.setAuthToken(null)
            localStorage.removeItem('refresh_token')
          }
        }
      }
      setIsLoading(false)
    }

    initAuth()
  }, [])

  const login = useCallback(async (credentials: LoginCredentials) => {
    setError(null)
    try {
      const response = await AuthService.login(credentials)
      setUser(response.user)
      navigate('/dashboard')
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Login failed'
      setError(errorMessage)
      throw err
    }
  }, [navigate])

  const logout = useCallback(async () => {
    try {
      await AuthService.logout()
    } catch (err) {
      // Even if logout API fails, clear local state
      console.error('Logout API failed:', err)
    } finally {
      setUser(null)
      navigate('/login')
    }
  }, [navigate])

  const refreshAuth = useCallback(async () => {
    try {
      await AuthService.refreshToken()
      const currentUser = await AuthService.getCurrentUser()
      setUser(currentUser)
    } catch (err) {
      setUser(null)
      throw err
    }
  }, [])

  // Set up automatic token refresh
  useEffect(() => {
    const refreshInterval = setInterval(async () => {
      if (user) {
        try {
          await refreshAuth()
        } catch (err) {
          console.error('Token refresh failed:', err)
        }
      }
    }, 10 * 60 * 1000) // Refresh every 10 minutes

    return () => clearInterval(refreshInterval)
  }, [user, refreshAuth])

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    logout,
    refreshAuth,
    error
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Guard component for protected routes
interface RequireAuthProps {
  children: ReactNode
  requiredRoles?: string[]
  requiredPermissions?: string[]
}

export function RequireAuth({ children, requiredRoles, requiredPermissions }: RequireAuthProps) {
  const { user, isAuthenticated, isLoading } = useAuth()
  const navigate = useNavigate()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate('/login', { replace: true })
    }
  }, [isAuthenticated, isLoading, navigate])

  if (isLoading) {
    return <div>Loading...</div>
  }

  if (!isAuthenticated || !user) {
    return null
  }

  // Check role requirements
  if (requiredRoles && requiredRoles.length > 0) {
    const hasRequiredRole = requiredRoles.some(role => user.roles.includes(role))
    if (!hasRequiredRole) {
      return <div>Access Denied: Insufficient role privileges</div>
    }
  }

  // Check permission requirements
  if (requiredPermissions && requiredPermissions.length > 0) {
    const hasRequiredPermissions = requiredPermissions.every(perm => user.permissions.includes(perm))
    if (!hasRequiredPermissions) {
      return <div>Access Denied: Insufficient permissions</div>
    }
  }

  return <>{children}</>
}