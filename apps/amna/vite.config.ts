import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import tsConfigPaths from 'vite-tsconfig-paths'
import { TanStackRouterVite } from '@tanstack/router-vite-plugin'
import { visualizer } from 'rollup-plugin-visualizer'
import { analyzer } from 'vite-bundle-analyzer'

export default defineConfig(({ mode }) => {
  // Load environment variables based on mode
  const env = loadEnv(mode, process.cwd(), '')
  
  // Bundle analysis plugins (only in analyze mode)
  const analysisPlugins = mode === 'analyze' ? [
    visualizer({
      filename: 'dist/stats.html',
      open: true,
      gzipSize: true,
      brotliSize: true,
      template: 'treemap' // sunburst, treemap, network
    }),
    analyzer({
      analyzerMode: 'server',
      analyzerPort: 8888,
      openAnalyzer: true,
      generateStatsFile: true,
      statsFilename: 'dist/bundle-stats.json'
    })
  ] : [];

  return {
    server: {
      port: 5001,
      proxy: {
        // Proxy API requests in development
        '/api': {
          target: env.VITE_API_BASE_URL || 'http://localhost:3000',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
        },
        // Proxy WebSocket connections
        '/ws': {
          target: env.VITE_WS_URL || 'ws://localhost:3000',
          ws: true,
          changeOrigin: true,
        },
      },
    },
    plugins: [
      react(),
      TanStackRouterVite(),
      tsConfigPaths({
        projects: ['./tsconfig.json'],
      }),
      ...analysisPlugins,
    ],
    define: {
      // Make env variables available globally
      __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
      __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
    },
    build: {
      // Library mode for package exports
      lib: mode === 'library' ? {
        entry: 'src/index.ts',
        name: 'AmnaApp',
        formats: ['es'],
        fileName: 'index'
      } : undefined,
      // Enable source maps in production for debugging
      sourcemap: mode === 'production' ? 'hidden' : true,
      // Chunk size warnings
      chunkSizeWarningLimit: 1000,
      rollupOptions: {
        output: {
          // Enhanced chunk splitting for better caching and performance
          manualChunks: (id) => {
            // Vendor chunks - more granular splitting
            if (id.includes('node_modules')) {
              // React ecosystem
              if (id.includes('react') || id.includes('react-dom')) {
                return 'vendor-react'
              }
              // TanStack ecosystem
              if (id.includes('@tanstack')) {
                return 'vendor-tanstack'
              }
              // UI libraries
              if (id.includes('@radix-ui')) {
                return 'vendor-radix'
              }
              if (id.includes('lucide-react') || id.includes('framer-motion')) {
                return 'vendor-ui'
              }
              // Shared packages
              if (id.includes('@luminar/shared-ui') || id.includes('@luminar/shared-core')) {
                return 'vendor-luminar'
              }
              // Large libraries that should be separate
              if (id.includes('lodash') || id.includes('date-fns')) {
                return 'vendor-utils'
              }
              // Other vendors
              return 'vendor-misc'
            }
            
            // Luminar shared packages
            if (id.includes('@luminar/shared-ui')) {
              if (id.includes('components')) {
                return 'luminar-components'
              }
              if (id.includes('hooks')) {
                return 'luminar-hooks'
              }
              return 'luminar-shared'
            }
            
            // Route-based chunks - more specific
            if (id.includes('src/routes/')) {
              // Group related routes
              if (id.includes('chat') || id.includes('conversation')) {
                return 'routes-chat'
              }
              if (id.includes('settings') || id.includes('preferences')) {
                return 'routes-settings'
              }
              if (id.includes('history') || id.includes('archive')) {
                return 'routes-history'
              }
              return 'routes-main'
            }
            
            // Component chunks - by feature
            if (id.includes('src/components/')) {
              if (id.includes('chat') || id.includes('message')) {
                return 'components-chat'
              }
              if (id.includes('ui') || id.includes('common')) {
                return 'components-ui'
              }
              if (id.includes('layout')) {
                return 'components-layout'
              }
              return 'components-misc'
            }
            
            // Utilities and libraries
            if (id.includes('src/lib/') || id.includes('src/utils/')) {
              if (id.includes('api') || id.includes('service')) {
                return 'utils-api'
              }
              return 'utils-misc'
            }
            
            // Store/state management
            if (id.includes('src/store') || id.includes('src/stores')) {
              return 'stores'
            }
          },
          // Optimize chunk names for better caching
          chunkFileNames: (chunkInfo) => {
            const facadeModuleId = chunkInfo.facadeModuleId
            ? chunkInfo.facadeModuleId.split('/').pop()?.replace('.tsx', '').replace('.ts', '')
            : 'chunk'
            return `js/[name]-[hash].js`
          },
          entryFileNames: 'js/[name]-[hash].js',
          assetFileNames: 'assets/[name]-[hash].[ext]',
        },
      },
    },
  }
})
